'use client';

import { useEffect, useState, useCallback, useRef } from 'react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';
import { LoadingSpinner } from '@/components/LoadingSpinner';
import Layout from '@/components/Layout';
import DiscoverTopBar from '@/components/discover/DiscoverTopBar';
import DiscoverSidebar from '@/components/discover/DiscoverSidebar';
import { ArticleItem } from '@/components/discover/ArticleItem';
import { ChatDrawer } from '@/components/discover/ChatDrawer';
import { Discover, Topic } from '@/types/discover';

interface TopicData {
  blogs: Discover[];
  pagination: {
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  };
}

const DiscoverPageContent = () => {
  const [topics, setTopics] = useState<Topic[]>([]);
  const [discoverMap, setDiscoverMap] = useState<Record<string, TopicData>>({});
  const [currentPage, setCurrentPage] = useState<Record<string, number>>({});
  const [loading, setLoading] = useState(true);
  const [expandedItems, setExpandedItems] = useState<number[]>([]);
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [selectedTab, setSelectedTab] = useState(0);
  const [summarizedOnly, setSummarizedOnly] = useState(false);
  const [importantOnly, setImportantOnly] = useState(false);
  const [selectedChat, setSelectedChat] = useState<{
    chatId?: string;
    discoverId: string;
    url?: string;
    title?: string;
  } | null>(null);
  const toggleExpand = (index: number) => {
    setExpandedItems(prev =>
      prev.includes(index)
        ? prev.filter(i => i !== index)
        : [...prev, index]
    );
  };
  const [topicLoading, setTopicLoading] = useState<Record<string, boolean>>({});

  const fetchTopicData = async (
    topic: string,
    page: number = 1,
    summarized: boolean = summarizedOnly,
    important: boolean = importantOnly
  ) => {
    setTopicLoading(prev => ({ ...prev, [topic]: true }));
    try {
      const res = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/discover?topic=${topic}&page=${page}&pageSize=10&summarized=${summarized}&important=${important}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      const data = await res.json();

      if (!res.ok) {
        throw new Error(data.message);
      }

      setDiscoverMap(prev => ({
        ...prev,
        [topic]: data,
      }));

      setCurrentPage(prev => ({
        ...prev,
        [topic]: page,
      }));

      setTimeout(() => {
        const tabPanel = document.querySelector(`[data-topic="${topic}"]`);
        if (tabPanel) {
          tabPanel.scrollTop = 0;
        }
      }, 0);
    } catch (err: any) {
      console.error('Error fetching data:', err.message);
      toast.error('获取数据失败');
    } finally {
      setTopicLoading(prev => ({ ...prev, [topic]: false }));
    }
  };

  const fetchTopics = async () => {
    try {
      const res = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/discover/topics`);
      const data = await res.json();
      if (!res.ok) {
        throw new Error(data.message);
      }
      setTopics(data.topics);
      return data.topics;
    } catch (err: any) {
      console.error('Error fetching topics:', err.message);
      toast.error('获取主题列表失败');
      return [];
    }
  };

  useEffect(() => {
    const init = async () => {
      try {
        const topicList = await fetchTopics();
        if (topicList.length > 0) {
          const savedTab = localStorage.getItem('selectedDiscoverTab');
          const savedTopicId = savedTab || topicList[0].id;
          const tabIndex = topicList.findIndex((t: { id: any }) => t.id === savedTopicId);

          setSelectedTab(tabIndex >= 0 ? tabIndex : 0);
          await fetchTopicData(savedTopicId);
        }
      } finally {
        setLoading(false);
      }
    };

    init();
  }, []);



  // 设备检测和清理触摸状态的副作用
  useEffect(() => {
    // 检测是否为触摸设备
    const checkTouchDevice = () => {
      const isMobileUserAgent = /Android|iPhone|iPad|iPod|Mobile/i.test(navigator.userAgent);
      const isSmallScreen = window.innerWidth < 768; // 只在真正的小屏幕设备上启用
      const hasTouchScreen = 'ontouchstart' in window || navigator.maxTouchPoints > 0;

      // 更严格的检测：必须是移动设备用户代理 OR (小屏幕 AND 触摸屏)
      // 这样可以避免在PC上误判
      const shouldEnableTouch = isMobileUserAgent || (isSmallScreen && hasTouchScreen);
      setIsTouchDevice(shouldEnableTouch);

      // 调试日志（生产环境可以移除）
      if (process.env.NODE_ENV === 'development') {
        console.log('Device detection:', {
          isMobileUserAgent,
          isSmallScreen,
          hasTouchScreen,
          shouldEnableTouch,
          userAgent: navigator.userAgent,
          screenWidth: window.innerWidth
        });
      }

      // 如果不是触摸设备，确保重置所有触摸相关状态
      if (!shouldEnableTouch) {
        setPullDistance(0);
        setIsPulling(false);
        setIsSwipingHorizontal(false);
        setSwipeDistance(0);
        touchStateRef.current.isTracking = false;
      }
    };

    checkTouchDevice();
    window.addEventListener('resize', checkTouchDevice);

    const handleVisibilityChange = () => {
      if (document.hidden) {
        // 页面隐藏时重置状态
        touchStateRef.current.isTracking = false;
        setPullDistance(0);
        setIsPulling(false);
        setIsSwipingHorizontal(false);
        setSwipeDistance(0);
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      window.removeEventListener('resize', checkTouchDevice);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  const [refreshing, setRefreshing] = useState(false);
  const [pullDistance, setPullDistance] = useState(0);
  const [isPulling, setIsPulling] = useState(false);

  // 水平滑动状态
  const [isSwipingHorizontal, setIsSwipingHorizontal] = useState(false);
  const [swipeDistance, setSwipeDistance] = useState(0);

  // 设备检测状态
  const [isTouchDevice, setIsTouchDevice] = useState(false);

  // 优化的触摸状态管理，支持水平和垂直滑动
  const touchStateRef = useRef({
    startX: 0,
    startY: 0,
    startTime: 0,
    isTracking: false,
    lastX: 0,
    lastY: 0,
    direction: null as 'horizontal' | 'vertical' | null,
    hasMovedSignificantly: false,
  });

  const threshold = 80; // 增加阈值以提供更好的用户体验
  const maxPullDistance = 120; // 最大拉动距离
  const dampingFactor = 0.6; // 阻尼系数，让拉动感觉更自然

  // 水平滑动相关常量
  const swipeThreshold = 80; // 水平滑动触发阈值

  const handlePullToRefresh = useCallback(
    async (topic: string) => {
      if (refreshing) return; // 防止重复触发

      setRefreshing(true);
      try {
        await fetchTopicData(topic, 1);
        toast.success('数据已更新', {
          duration: 1500,
        });
      } finally {
        setRefreshing(false);
      }
    },
    [refreshing] // 添加 refreshing 依赖
  );

  // 水平滑动切换主题（暂时移除，稍后在 handleSelectTopic 之后定义）

  // 优化的触摸开始处理，支持水平和垂直滑动
  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    // 双重检查：确保在PC端完全不处理触摸事件
    if (!isTouchDevice || e.touches.length !== 1 || refreshing) {
      return;
    }

    const touch = e.touches[0];
    touchStateRef.current = {
      startX: touch.clientX,
      startY: touch.clientY,
      startTime: Date.now(),
      isTracking: true,
      lastX: touch.clientX,
      lastY: touch.clientY,
      direction: null,
      hasMovedSignificantly: false,
    };

    setIsPulling(false);
    setPullDistance(0);
    setIsSwipingHorizontal(false);
    setSwipeDistance(0);
  }, [isTouchDevice, refreshing]);

  // 优化的触摸移动处理，支持水平和垂直滑动检测
  const handleTouchMove = useCallback((e: React.TouchEvent) => {
    // 双重检查：确保在PC端完全不处理触摸事件
    if (!isTouchDevice || !touchStateRef.current.isTracking || e.touches.length !== 1 || refreshing) {
      return;
    }

    const panel = e.currentTarget as HTMLElement;
    const touch = e.touches[0];
    const currentX = touch.clientX;
    const currentY = touch.clientY;
    const deltaX = currentX - touchStateRef.current.startX;
    const deltaY = currentY - touchStateRef.current.startY;
    const isAtTop = panel.scrollTop <= 0;

    // 确定滑动方向（只在第一次显著移动时确定）
    if (!touchStateRef.current.hasMovedSignificantly) {
      const absX = Math.abs(deltaX);
      const absY = Math.abs(deltaY);

      // 需要移动至少 10px 才确定方向
      if (absX > 10 || absY > 10) {
        touchStateRef.current.hasMovedSignificantly = true;

        // 水平滑动优先级更高（比例 1.5:1）
        if (absX > absY * 1.5) {
          touchStateRef.current.direction = 'horizontal';
        } else if (absY > absX * 1.5) {
          touchStateRef.current.direction = 'vertical';
        }
      }
    }

    // 处理水平滑动（主题切换）
    if (touchStateRef.current.direction === 'horizontal') {
      requestAnimationFrame(() => {
        const dampedDistance = deltaX * 0.3; // 水平滑动阻尼
        setSwipeDistance(dampedDistance);
        setIsSwipingHorizontal(true);

        // 更新最后位置
        touchStateRef.current.lastX = currentX;
      });

      // 阻止默认行为
      e.preventDefault();
    }
    // 处理垂直滑动（下拉刷新）
    else if (touchStateRef.current.direction === 'vertical' && isAtTop && deltaY > 0) {
      requestAnimationFrame(() => {
        // 应用阻尼效果，让拉动感觉更自然
        const dampedDistance = Math.min(deltaY * dampingFactor, maxPullDistance);

        setPullDistance(dampedDistance);
        setIsPulling(true);

        // 更新最后位置
        touchStateRef.current.lastY = currentY;
      });

      // 阻止默认滚动行为
      e.preventDefault();
    } else if (isPulling && deltaY <= 0) {
      // 如果用户向上滑动，重置下拉刷新状态
      requestAnimationFrame(() => {
        setPullDistance(0);
        setIsPulling(false);
      });
    }
  }, [isTouchDevice, refreshing, isPulling, dampingFactor, maxPullDistance]);

  // 优化的触摸结束处理，支持水平和垂直滑动
  const handleTouchEnd = useCallback(async (_e: React.TouchEvent, topic: string) => {
    // 双重检查：确保在PC端完全不处理触摸事件
    if (!isTouchDevice || !touchStateRef.current.isTracking || refreshing) {
      return;
    }

    const endTime = Date.now();
    const duration = endTime - touchStateRef.current.startTime;
    const deltaX = touchStateRef.current.lastX - touchStateRef.current.startX;

    // 重置触摸状态
    touchStateRef.current.isTracking = false;

    // 处理水平滑动（主题切换）
    if (touchStateRef.current.direction === 'horizontal' && Math.abs(deltaX) >= swipeThreshold && duration > 100) {
      if (topics.length > 1) {
        let newIndex = selectedTab;
        if (deltaX < 0 && selectedTab < topics.length - 1) {
          // 向左滑动，切换到下一个主题
          newIndex = selectedTab + 1;
        } else if (deltaX > 0 && selectedTab > 0) {
          // 向右滑动，切换到上一个主题
          newIndex = selectedTab - 1;
        }

        if (newIndex !== selectedTab) {
          const newTopic = topics[newIndex];
          setSelectedTab(newIndex);
          localStorage.setItem('selectedDiscoverTab', newTopic.id);
          if (!discoverMap[newTopic.id]) {
            fetchTopicData(newTopic.id);
          }
          toast.success(`切换到: ${newTopic.name}`, {
            duration: 1000,
          });
        }
      }
    }
    // 处理垂直滑动（下拉刷新）
    else if (touchStateRef.current.direction === 'vertical' && isPulling && pullDistance >= threshold && duration > 100) {
      await handlePullToRefresh(topic);
    }

    // 重置UI状态
    setPullDistance(0);
    setIsPulling(false);
    setIsSwipingHorizontal(false);
    setSwipeDistance(0);
  }, [isTouchDevice, refreshing, isPulling, pullDistance, threshold, handlePullToRefresh, swipeThreshold, topics, selectedTab, discoverMap]);

  // 主题切换统一处理
  const handleSelectTopic = (topicId: string, idx: number) => {
    setSelectedTab(idx);
    localStorage.setItem('selectedDiscoverTab', topicId);
    if (!discoverMap[topicId]) {
      fetchTopicData(topicId);
    }
  };



  const selectedTopic = topics[selectedTab];

  return loading ? (
    <LoadingSpinner />
  ) : (
    <Layout>
      <div className="flex flex-col min-h-screen bg-light-50 dark:bg-dark-50">
        <DiscoverTopBar
          topics={topics}
          selectedTopicId={selectedTopic?.id}
          onSelectTopic={handleSelectTopic}
          summarizedOnly={summarizedOnly}
          importantOnly={importantOnly}
          onSummarizedChange={checked => {
            setSummarizedOnly(checked);
            topics.forEach(topic => {
              fetchTopicData(topic.id, 1, checked, importantOnly);
            });
          }}
          onImportantChange={checked => {
            setImportantOnly(checked);
            topics.forEach(topic => {
              if (discoverMap[topic.id]) {
                fetchTopicData(topic.id, 1, summarizedOnly, checked);
              }
            });
          }}
        />
        <main className="flex-1 flex flex-col pt-[104px] lg:pt-[72px]">
          <div className="flex flex-1 min-h-0">
            <DiscoverSidebar
              topics={topics}
              selectedTopicId={selectedTopic?.id}
              onSelectTopic={handleSelectTopic}
              className="h-full"
            />
            <div className="flex-1 lg:pl-5 pr-1 lg:pr-5">
              <div className="flex-1 min-h-0 relative">
                {topics.length > 0 && selectedTopic && (
                  <div
                    className={cn(
                      "flex-1 min-h-0 relative overflow-y-auto",
                      // 优化滚动性能
                      "scroll-smooth"
                    )}
                    data-topic={selectedTopic.id}
                    {...(isTouchDevice ? {
                      onTouchStart: handleTouchStart,
                      onTouchMove: handleTouchMove,
                      onTouchEnd: (e: React.TouchEvent) => handleTouchEnd(e, selectedTopic.id)
                    } : {})}
                    style={{
                      // 优化触摸滚动（仅在触摸设备上应用）
                      ...(isTouchDevice ? { WebkitOverflowScrolling: 'touch' } : {}),
                      // 在PC上确保正常滚动
                      ...(!isTouchDevice ? { overflowY: 'auto' } : {}),
                    }}
                  >
                    <div
                      className={cn(
                        "transition-transform ease-out relative",
                        refreshing ? "duration-300" : "duration-200",
                        isSwipingHorizontal ? "duration-100" : ""
                      )}
                      style={{
                        // 只在触摸设备上应用transform
                        ...(isTouchDevice ? {
                          transform: `translateY(${pullDistance}px) translateX(${swipeDistance}px)`,
                          willChange: isPulling || refreshing || isSwipingHorizontal ? 'transform' : 'auto',
                        } : {}),
                      }}
                    >
                      {/* 水平滑动指示器（仅触摸设备） */}
                      {isTouchDevice && isSwipingHorizontal && (
                        <>
                          {/* 左侧指示器（上一个主题） */}
                          {selectedTab > 0 && (
                            <div
                              className={cn(
                                "absolute left-0 top-1/2 -translate-y-1/2 flex items-center justify-center w-20 h-16 bg-blue-500/20 dark:bg-blue-400/20 rounded-r-lg transition-all duration-200",
                                swipeDistance > 30 ? "opacity-100 scale-110" : "opacity-60"
                              )}
                              style={{ transform: `translateX(${Math.max(swipeDistance - 50, -20)}px)` }}
                            >
                              <div className="text-center">
                                <div className="text-xs text-blue-600 dark:text-blue-400 font-medium">
                                  ← {topics[selectedTab - 1]?.name}
                                </div>
                              </div>
                            </div>
                          )}

                          {/* 右侧指示器（下一个主题） */}
                          {selectedTab < topics.length - 1 && (
                            <div
                              className={cn(
                                "absolute right-0 top-1/2 -translate-y-1/2 flex items-center justify-center w-20 h-16 bg-green-500/20 dark:bg-green-400/20 rounded-l-lg transition-all duration-200",
                                swipeDistance < -30 ? "opacity-100 scale-110" : "opacity-60"
                              )}
                              style={{ transform: `translateX(${Math.min(swipeDistance + 50, 20)}px)` }}
                            >
                              <div className="text-center">
                                <div className="text-xs text-green-600 dark:text-green-400 font-medium">
                                  {topics[selectedTab + 1]?.name} →
                                </div>
                              </div>
                            </div>
                          )}
                        </>
                      )}

                      {/* 优化的刷新指示器（仅触摸设备） */}
                      {isTouchDevice && (
                        <div
                          className={cn(
                            "absolute top-0 left-0 right-0 flex flex-col justify-center items-center h-16 -translate-y-full transition-all duration-300",
                            (isPulling || refreshing) ? "opacity-100" : "opacity-0"
                          )}
                        >
                        <div className="flex items-center space-x-2">
                          {refreshing ? (
                            <>
                              <LoadingSpinner />
                              <span className="text-sm text-black/70 dark:text-white/70">
                                正在刷新...
                              </span>
                            </>
                          ) : (
                            <>
                              <div
                                className={cn(
                                  "w-4 h-4 rounded-full border-2 transition-all duration-200",
                                  pullDistance >= threshold
                                    ? "border-green-500 bg-green-500/20 rotate-180"
                                    : "border-gray-400 dark:border-gray-500"
                                )}
                              >
                                <div className={cn(
                                  "w-full h-full rounded-full transition-all duration-200",
                                  pullDistance >= threshold ? "bg-green-500" : ""
                                )} />
                              </div>
                              <span className="text-sm text-black/70 dark:text-white/70">
                                {pullDistance >= threshold ? '松开刷新' : '下拉刷新'}
                              </span>
                            </>
                          )}
                        </div>
                        {/* 进度指示器 */}
                        {!refreshing && (
                          <div className="w-16 h-1 bg-gray-200 dark:bg-gray-700 rounded-full mt-2 overflow-hidden">
                            <div
                              className="h-full bg-blue-500 transition-all duration-100 ease-out"
                              style={{
                                width: `${Math.min((pullDistance / threshold) * 100, 100)}%`
                              }}
                            />
                          </div>
                        )}
                        </div>
                      )}
                      <div className="flex flex-col space-y-1.5 p-1 pt-8 pb-24">
                        {discoverMap[selectedTopic.id]?.blogs?.map((item, i) => (
                          <ArticleItem
                            key={item.id}
                            item={item}
                            index={i}
                            expandedItems={expandedItems}
                            toggleExpand={toggleExpand}
                            onSelect={item => {
                              setSelectedChat({
                                chatId: item.chatId,
                                discoverId: item.id,
                                url: !item.chatId ? item.url : undefined,
                                title: `追踪： ${item.titleZh || item.title}`,
                              });
                              setIsDrawerOpen(true);
                            }}
                          />
                        ))}
                      </div>

                    </div>
                    {topicLoading[selectedTopic.id] && (
                      <div className="absolute inset-0 bg-black/5 dark:bg-white/5 flex items-center justify-center z-10 backdrop-blur-[1px]">
                        <LoadingSpinner />
                      </div>
                    )}
                  </div>
                )}
              </div>
              {/* 右侧抽屉 */}
              {isDrawerOpen && (
                <ChatDrawer
                  isOpen={isDrawerOpen}
                  onClose={() => {
                    setIsDrawerOpen(false);
                    setSelectedChat(null);
                  }}
                  selectedChat={selectedChat}
                  onChatIdCreated={newChatId => {
                    setSelectedChat(prev =>
                      prev
                        ? {
                            ...prev,
                            chatId: newChatId,
                          }
                        : null
                    );
  
                    if (selectedChat?.discoverId) {
                      setDiscoverMap(prev => {
                        const newMap = { ...prev };
                        Object.keys(newMap).forEach(topic => {
                          if (newMap[topic]?.blogs) {
                            newMap[topic] = {
                              ...newMap[topic],
                              blogs: newMap[topic].blogs.map(item =>
                                item.id === selectedChat.discoverId
                                  ? { ...item, chatId: newChatId }
                                  : item
                              ),
                            };
                          }
                        });
                        return newMap;
                      });
                    }
                  }}
                />
              )}
            </div>
          </div>
        </main>

        {/* 吸底分页控制和滑动提示 */}
        {topics.length > 0 && selectedTopic && discoverMap[selectedTopic.id]?.pagination && (
          <div className="fixed bottom-0 left-0 right-0 z-50 bg-white/95 dark:bg-dark-50/95 backdrop-blur-md border-t border-black/10 dark:border-white/10 shadow-lg">
            {/* 分页控制 */}
            <div className="flex justify-center items-center space-x-2 sm:space-x-4 py-3 px-2 sm:px-4">
              <button
                onClick={() =>
                  fetchTopicData(selectedTopic.id, currentPage[selectedTopic.id] - 1)
                }
                disabled={currentPage[selectedTopic.id] <= 1}
                className={cn(
                  "flex items-center space-x-1 px-3 sm:px-4 py-2 rounded-full text-xs sm:text-sm font-medium transition-all duration-200",
                  currentPage[selectedTopic.id] <= 1
                    ? "bg-gray-100 dark:bg-gray-800 text-gray-400 dark:text-gray-600 cursor-not-allowed"
                    : "bg-blue-500 hover:bg-blue-600 text-white shadow-md hover:shadow-lg active:scale-95 sm:hover:scale-105"
                )}
              >
                <span>←</span>
                <span className="hidden sm:inline">上一页</span>
                <span className="sm:hidden">上页</span>
              </button>

              <div className="flex items-center space-x-2 px-3 sm:px-4 py-2 bg-gray-100 dark:bg-gray-800 rounded-full">
                <span className="text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300">
                  {currentPage[selectedTopic.id]} / {discoverMap[selectedTopic.id].pagination.totalPages}
                </span>
              </div>

              <button
                onClick={() =>
                  fetchTopicData(selectedTopic.id, currentPage[selectedTopic.id] + 1)
                }
                disabled={
                  currentPage[selectedTopic.id] >=
                  discoverMap[selectedTopic.id].pagination.totalPages
                }
                className={cn(
                  "flex items-center space-x-1 px-3 sm:px-4 py-2 rounded-full text-xs sm:text-sm font-medium transition-all duration-200",
                  currentPage[selectedTopic.id] >= discoverMap[selectedTopic.id].pagination.totalPages
                    ? "bg-gray-100 dark:bg-gray-800 text-gray-400 dark:text-gray-600 cursor-not-allowed"
                    : "bg-blue-500 hover:bg-blue-600 text-white shadow-md hover:shadow-lg active:scale-95 sm:hover:scale-105"
                )}
              >
                <span className="hidden sm:inline">下一页</span>
                <span className="sm:hidden">下页</span>
                <span>→</span>
              </button>
            </div>

            {/* 滑动提示（仅移动设备） */}
            {isTouchDevice && topics.length > 1 && (
              <div className="flex justify-center items-center pb-2 px-4">
                <div className="flex items-center space-x-1 sm:space-x-2 px-2 sm:px-3 py-1 bg-gray-100/80 dark:bg-gray-800/80 rounded-full">
                  <span className="text-xs text-gray-500 dark:text-gray-400">←</span>
                  <span className="text-xs text-gray-500 dark:text-gray-400 font-medium">
                    <span className="hidden sm:inline">左右滑动切换主题</span>
                    <span className="sm:hidden">滑动切换</span>
                  </span>
                  <span className="text-xs text-gray-500 dark:text-gray-400">→</span>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </Layout>
  );
};

export default DiscoverPageContent;
