import { X } from 'lucide-react';
import { cn } from '@/lib/utils';
import ChatWindow from '@/components/ChatWindow';
import DeleteChat from '../DeleteChat';
import { useEffect, useRef, useState } from 'react';

interface ChatDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  selectedChat: {
    chatId?: string;
    discoverId?: string;
    url?: string;
    title?: string;
  } | null;
  onChatIdCreated: (newChatId: string) => void;
}

export const ChatDrawer = ({ isOpen, onClose, selectedChat, onChatIdCreated }: ChatDrawerProps) => {
  const drawerRef = useRef<HTMLDivElement>(null);

  // 判断是否为移动端，仅移动端启用滑动关闭
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      if (typeof window !== 'undefined') {
        // 1. 优先用 matchMedia 判断
        if (window.matchMedia('(pointer: coarse)').matches) {
          setIsMobile(true);
          return;
        }
        // 2. 兜底用 userAgent
        const ua = navigator.userAgent;
        if (/Android|iPhone|iPad|iPod|Mobile/i.test(ua)) {
          setIsMobile(true);
          return;
        }
      }
      setIsMobile(false);
    };
    checkMobile();
  }, []);

  // 手势状态
  const touchStartRef = useRef<{ x: number; y: number } | null>(null);
  const touchMovedRef = useRef<boolean>(false);

  const onTouchStart = (e: React.TouchEvent) => {
    if (!isMobile) return;
    if (e.touches.length !== 1) return; // 只处理单指
    touchStartRef.current = {
      x: e.touches[0].clientX,
      y: e.touches[0].clientY,
    };
    touchMovedRef.current = false;
  };

  const onTouchMove = (e: React.TouchEvent) => {
    if (!isMobile) return;
    if (!touchStartRef.current) return;
    if (e.touches.length !== 1) return;
    const deltaX = e.touches[0].clientX - touchStartRef.current.x;
    const deltaY = e.touches[0].clientY - touchStartRef.current.y;
    // 仅当横向滑动明显大于纵向时阻止默认，避免页面滚动
    if (Math.abs(deltaX) > 10 && Math.abs(deltaX) > Math.abs(deltaY) * 2) {
      e.preventDefault();
      touchMovedRef.current = true;
    }
  };

  const onTouchEnd = (e: React.TouchEvent) => {
    if (!isMobile) return;
    if (!touchStartRef.current) return;
    if (e.changedTouches.length !== 1) return;
    const touchEnd = {
      x: e.changedTouches[0].clientX,
      y: e.changedTouches[0].clientY,
    };
    const deltaX = touchEnd.x - touchStartRef.current.x;
    const deltaY = touchEnd.y - touchStartRef.current.y;
    // 只响应右滑，且横向距离大于50px，且横向大于纵向
    if (deltaX > 50 && Math.abs(deltaX) > Math.abs(deltaY) * 1.5) {
      onClose();
    }
    touchStartRef.current = null;
    touchMovedRef.current = false;
  };
  
  // 阻止浏览器历史导航
  useEffect(() => {
    const preventHistoryNavigation = () => {
      if (isOpen) {
        window.history.pushState(null, '', window.location.href);
      }
    };
    
    window.addEventListener('popstate', preventHistoryNavigation);
    
    if (isOpen) {
      window.history.pushState(null, '', window.location.href);
    }
    
    return () => {
      window.removeEventListener('popstate', preventHistoryNavigation);
    };
  }, [isOpen]);

  return (
    <>
      <div
        className={cn(
          "fixed inset-0 bg-black/20 dark:bg-white/10 transition-opacity duration-500 z-[60]",
          isOpen ? "opacity-100" : "opacity-0 pointer-events-none"
        )}
        onClick={onClose}
      />
      <div
        ref={drawerRef}
        className={cn(
          "fixed top-0 right-0 h-full overflow-y-auto w-full lg:w-[80%] bg-light-primary dark:bg-dark-primary shadow-[-8px_0_30px_rgba(0,0,0,0.1)] z-[70]",
          "ease-out transition-opacity duration-300 md:transform md:transition-transform md:duration-500",
          isOpen
            ? "opacity-100 md:translate-x-0"
            : "opacity-0 pointer-events-none md:opacity-100 md:translate-x-[105%]"
        )}
        // 仅移动端绑定触控事件
        {...(isMobile
          ? {
              onTouchStart,
              onTouchMove,
              onTouchEnd,
            }
          : {})}
      >
        <div
          className={cn(
            "sticky z-40 top-0 left-0 right-0 flex items-center justify-between w-full py-2 px-4 text-sm",
            "shadow-xl dark:shadow-lg",
            "bg-gradient-to-r from-[#e3f0ff] to-[#f8fafc] dark:from-[#23272e] dark:to-[#181c20] backdrop-blur-md",
            "border-b border-light-200 dark:border-dark-200 transition-all duration-300"
          )}
        >
          {/* 左侧关闭按钮 */}
          <button
            onClick={onClose}
            className="flex items-center justify-center rounded-lg p-2 text-black dark:text-white hover:text-[#24A0ED] dark:hover:text-[#24A0ED] transition-colors duration-150"
            aria-label="关闭"
          >
            <X className="h-5 w-5" />
          </button>

          {/* 中间标题 */}
          {selectedChat?.chatId && (
            <div className="flex-1 flex flex-col items-center justify-center mx-2">
              <p className="truncate max-w-[200px] text-center text-base font-semibold">{selectedChat?.title}</p>
            </div>
          )}

          {/* 右侧操作按钮 */}
          <div className="w-[40px] flex justify-center">
            {selectedChat?.chatId && (
              <DeleteChat
                redirect={false}
                chatId={selectedChat.chatId}
                chats={[]}
                setChats={() => {}}
                onClose={onClose}
              />
            )}
          </div>
        </div>
        <div className="flex-1 flex flex-col px-4">
          <ChatWindow
            id={selectedChat?.chatId}
            initialQuery={selectedChat?.chatId ? undefined : selectedChat?.url ? `Summary: ${selectedChat.url}` : undefined}
            discoverId={selectedChat?.discoverId}
            title={selectedChat?.title}
            onChatIdCreated={onChatIdCreated}
            showCloseButton={true}
            autoScroll={false}
          />
        </div>
      </div>
    </>
  );
};
